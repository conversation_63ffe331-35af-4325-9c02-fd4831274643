from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
from contextlib import asynccontextmanager
import os
import logging
import time
from dotenv import load_dotenv

from .routers import auth, virtual_machine, system
from .database import init_db

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("app.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Lifespan event handler
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("Starting up Proxmox Lab Management System...")
    init_db()
    logger.info("Database initialized successfully")
    yield
    # Shutdown
    logger.info("Shutting down Proxmox Lab Management System...")

# Create FastAPI app
app = FastAPI(
    title="Proxmox Lab Management System",
    description="API for managing virtual machines and containers in a Proxmox lab environment",
    version="1.0.0",
    docs_url="/docs" if os.getenv("DEBUG", "False").lower() == "true" else None,
    redoc_url="/redoc" if os.getenv("DEBUG", "False").lower() == "true" else None,
    lifespan=lifespan,
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv("ALLOWED_ORIGINS", "http://localhost:3000").split(","),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add request logging middleware
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()

    # Log request
    logger.info(f"Request: {request.method} {request.url}")

    response = await call_next(request)

    # Log response
    process_time = time.time() - start_time
    logger.info(f"Response: {response.status_code} - {process_time:.4f}s")

    return response

# Global exception handlers
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    logger.error(f"Validation error: {exc.errors()}")
    return JSONResponse(
        status_code=422,
        content={
            "error": "Validation Error",
            "details": exc.errors(),
            "message": "Please check your input data"
        }
    )

@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    logger.error(f"HTTP error: {exc.status_code} - {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": "HTTP Error",
            "message": exc.detail,
            "status_code": exc.status_code
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    logger.error(f"Unexpected error: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal Server Error",
            "message": "An unexpected error occurred. Please try again later."
        }
    )

# Include routers
app.include_router(auth.router)
app.include_router(virtual_machine.router)
app.include_router(system.router)

@app.get("/")
async def root():
    """Root endpoint."""
    return JSONResponse({
        "status": "success",
        "message": "Lab Management System API is running",
        "version": "1.0.0"
    })

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    from .services.proxmox import ProxmoxService

    health_status = {
        "status": "healthy",
        "components": {
            "api": "up",
            "database": "unknown",
            "proxmox": "unknown"
        }
    }

    # Test Proxmox connection
    try:
        proxmox = ProxmoxService()
        version = proxmox.proxmox.version.get()
        health_status["components"]["proxmox"] = "up"
        if version:
            health_status["proxmox_version"] = version.get("version", "Unknown")
    except Exception as e:
        health_status["components"]["proxmox"] = "down"
        health_status["proxmox_error"] = str(e)
        health_status["status"] = "degraded"
        logger.warning(f"Proxmox health check failed: {str(e)}")

    return JSONResponse(health_status)

if __name__ == "__main__":
    import uvicorn
    
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8000"))
    debug = os.getenv("DEBUG", "False").lower() == "true"
    
    uvicorn.run(
        "app.main:app",
        host=host,
        port=port,
        reload=debug
    )