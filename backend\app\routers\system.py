from fastapi import APIRouter, Depends, HTTPException
from typing import List, Dict, Any
from ..models.user import User, UserRole
from ..routers.auth import get_current_user
from ..services.proxmox import ProxmoxService

router = APIRouter(prefix="/system", tags=["system"])

@router.get("/nodes")
async def get_nodes(
    current_user: User = Depends(get_current_user)
) -> List[Dict[str, Any]]:
    """Get list of available Proxmox nodes."""
    # Only admins and instructors can view system information
    if current_user.role not in [UserRole.ADMIN, UserRole.INSTRUCTOR]:
        raise HTTPException(
            status_code=403,
            detail="Insufficient permissions to view system information"
        )
    
    proxmox = ProxmoxService()
    try:
        nodes = await proxmox.get_nodes()
        return nodes
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get nodes: {str(e)}"
        )

@router.get("/cluster-status")
async def get_cluster_status(
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """Get overall cluster status and health."""
    # Only admins and instructors can view system information
    if current_user.role not in [UserRole.ADMIN, UserRole.INSTRUCTOR]:
        raise HTTPException(
            status_code=403,
            detail="Insufficient permissions to view system information"
        )
    
    proxmox = ProxmoxService()
    try:
        status = await proxmox.get_cluster_status()
        return status
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get cluster status: {str(e)}"
        )

@router.get("/proxmox-info")
async def get_proxmox_info(
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """Get Proxmox server information."""
    # Only admins can view detailed Proxmox information
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=403,
            detail="Admin access required"
        )
    
    proxmox = ProxmoxService()
    try:
        # Get version information
        version = proxmox.proxmox.version.get()
        
        # Get cluster status
        cluster_status = await proxmox.get_cluster_status()
        
        return {
            "version": version.get("version", "Unknown"),
            "release": version.get("release", "Unknown"),
            "cluster": cluster_status,
            "connection_status": "connected"
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get Proxmox information: {str(e)}"
        )

@router.get("/health")
async def system_health_check() -> Dict[str, Any]:
    """Comprehensive system health check."""
    health_status = {
        "status": "healthy",
        "components": {
            "api": "up",
            "database": "unknown",
            "proxmox": "unknown"
        },
        "timestamp": None
    }
    
    # Test Proxmox connection
    try:
        proxmox = ProxmoxService()
        version = proxmox.proxmox.version.get()
        health_status["components"]["proxmox"] = "up"
        health_status["proxmox_version"] = version.get("version", "Unknown")
    except Exception as e:
        health_status["components"]["proxmox"] = "down"
        health_status["proxmox_error"] = str(e)
        health_status["status"] = "degraded"
    
    # Add timestamp
    from datetime import datetime
    health_status["timestamp"] = datetime.utcnow().isoformat()
    
    return health_status
